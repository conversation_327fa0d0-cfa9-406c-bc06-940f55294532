'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { revalidatePath } from 'next/cache'

export interface CreateModuleRequest {
    name: string;
}

export interface Module {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
}

export interface ModuleErrorResponse {
    message: string;
    errors?: Record<string, string[]>;
}

export async function createModule(moduleData: CreateModuleRequest): Promise<Module | ModuleErrorResponse> {
    try {
        console.log('Creating module with data:', moduleData);
        console.log('Base URL:', process.env.NEXT_PUBLIC_BACKEND_URL);

        const { data } = await axiosInstance.post<Module>(
            `/modules`,
            moduleData
        )
        console.log('Module created successfully:', data);
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error creating module:', error);
        console.error('Error response:', error.response?.data);
        console.error('Error status:', error.response?.status);
        console.error('Error config:', error.config);

        if (error.response?.data) {
            return error.response.data as ModuleErrorResponse
        }
        throw error
    }
}

export async function updateModule(id: number, moduleData: Partial<CreateModuleRequest>): Promise<Module | ModuleErrorResponse> {
    try {
        const { data } = await axiosInstance.put<Module>(
            `/modules/${id}`,
            moduleData
        )
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error updating module:', error.response?.data)
        if (error.response?.data) {
            return error.response.data as ModuleErrorResponse
        }
        throw error
    }
}

export async function deleteModule(id: number): Promise<{ success: boolean }> {
    try {
        await axiosInstance.delete(`/modules/${id}`)
        revalidatePath('/dashboard')
        return { success: true }
    } catch (error) {
        console.error('Error deleting module:', error)
        throw error
    }
}
