import { getAllSections } from "@/lib/server/actions/section/sectionActions";
import SectionsClient from "./SectionsClient";

export default async function SectionsPage() {
    let sections = [];
    
    try {
        sections = await getAllSections();
    } catch (error) {
        console.error('Error fetching sections:', error);
        sections = [];
    }

    return <SectionsClient initialSections={sections} />;
}
