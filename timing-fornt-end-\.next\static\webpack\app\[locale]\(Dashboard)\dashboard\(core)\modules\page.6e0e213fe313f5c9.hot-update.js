"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(core)/modules/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/ModulesClient.tsx":
/*!*********************************************************************************!*\
  !*** ./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/ModulesClient.tsx ***!
  \*********************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModulesClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ui/components/local/Dashboard/DashCrudContent */ \"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/DashCrudContent.tsx\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Timer,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Timer,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Timer,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Timer,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _CreateModuleDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateModuleDialog */ \"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx\");\n/* harmony import */ var _lib_server_actions_module_moduleActions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/server/actions/module/moduleActions */ \"(app-pages-browser)/./src/lib/server/actions/module/moduleActions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ModulesClient(param) {\n    let { initialModules } = param;\n    _s();\n    const [modules, setModules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialModules);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteDialog, setDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        module: null,\n        isDeleting: false\n    });\n    const refreshModules = async ()=>{\n        setIsRefreshing(true);\n        try {\n            const updatedModules = await (0,_lib_server_actions_module_moduleActions__WEBPACK_IMPORTED_MODULE_5__.getAllModules)();\n            setModules(updatedModules);\n        } catch (error) {\n            console.error('Error refreshing modules:', error);\n        } finally{\n            setIsRefreshing(false);\n        }\n    };\n    const handleDeleteClick = (module)=>{\n        setDeleteDialog({\n            isOpen: true,\n            module,\n            isDeleting: false\n        });\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!deleteDialog.module) return;\n        setDeleteDialog((prev)=>({\n                ...prev,\n                isDeleting: true\n            }));\n        try {\n            await (0,_lib_server_actions_module_moduleActions__WEBPACK_IMPORTED_MODULE_5__.deleteModule)(deleteDialog.module.id);\n            // Remove the module from the local state immediately\n            setModules((prev)=>prev.filter((m)=>m.id !== deleteDialog.module.id));\n            setDeleteDialog({\n                isOpen: false,\n                module: null,\n                isDeleting: false\n            });\n        } catch (error) {\n            console.error('Error deleting module:', error);\n            setDeleteDialog((prev)=>({\n                    ...prev,\n                    isDeleting: false\n                }));\n        // You could add a toast notification here for error feedback\n        }\n    };\n    const handleDeleteCancel = ()=>{\n        if (!deleteDialog.isDeleting) {\n            setDeleteDialog({\n                isOpen: false,\n                module: null,\n                isDeleting: false\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContent, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContenTitle, {\n                children: \"Modules\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 83,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentStat, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentStatItem, {\n                    title: \"Total Modules\",\n                    value: modules.length.toString(),\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 80\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 27\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentAction, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateModuleDialog__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onSuccess: refreshModules\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 91,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentTable, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableThead, {\n                        list: [\n                            'Module Name',\n                            'Settings'\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTdMain, {\n                                        value: module.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTd, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/modules/\".concat(module.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"text-green-700 dark:text-green-400\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteClick(module),\n                                                    className: \"p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded\",\n                                                    title: \"Delete module\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"text-red-600 dark:text-red-400\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/modules/\".concat(module.id, \"/schedule\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"text-secondary dark:text-dark-secondary\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, module.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 25\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 94,\n                columnNumber: 13\n            }, this),\n            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4 text-gray-500\",\n                children: \"Refreshing modules...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 122,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n        lineNumber: 82,\n        columnNumber: 9\n    }, this);\n}\n_s(ModulesClient, \"ksLm/+ueBCq0amAwjYM0aCvQm/M=\");\n_c = ModulesClient;\nvar _c;\n$RefreshReg$(_c, \"ModulesClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/ModulesClient.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"68c4272fe2c9\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWVcXEZpbmFsIHByb2plY3RcXHRpbWluZy1mb3JudC1lbmQtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2OGM0MjcyZmUyYzlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

});