<?php

namespace App\Http\Controllers\Api\Core;

use App\Http\Controllers\Controller;
use App\Models\Api\Main\Year;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class YearsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $years = Year::with('department')->orderBy('created_at', 'desc')->paginate(10);
        return response()->json($years);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'year' => 'required|integer|min:1',
            'department_id' => 'required|exists:departments,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $year = Year::create([
                'name' => $request->name,
                'year' => $request->year,
                'department_id' => $request->department_id,
            ]);

            return response()->json([
                'message' => 'Year created successfully',
                'year' => $year->load('department')
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create year',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Year $year)
    {
        return response()->json([
            'message' => 'Year fetched successfully',
            'year' => $year->load('department')
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Year $year)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'year' => 'sometimes|required|integer|min:1',
            'department_id' => 'sometimes|required|exists:departments,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $year->update($request->only(['name', 'year', 'department_id']));

            return response()->json([
                'message' => 'Year updated successfully',
                'year' => $year->load('department')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update year',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Year $year)
    {
        try {
            $year->delete();

            return response()->json([
                'message' => 'Year deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete year',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
