"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateDepartmentForm from "@/lib/ui/forms/department/CreateDepartmentForm";
import { Plus } from "lucide-react";

interface CreateDepartmentDialogProps {
    onSuccess?: () => void;
}

export default function CreateDepartmentDialog({ onSuccess }: CreateDepartmentDialogProps) {
    const [open, setOpen] = useState(false);

    const handleCloseDialog = () => {
        setOpen(false);
        onSuccess?.();
    };

    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Department
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Department">
                <CreateDepartmentForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
}
