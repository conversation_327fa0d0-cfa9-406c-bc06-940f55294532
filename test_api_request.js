// Simple test to check if the API is working
const http = require('http');

function testAPI(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 8001,
            path: `/api${path}`,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    status: res.statusCode,
                    headers: res.headers,
                    body: body
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

async function runTests() {
    console.log('Testing API endpoints...\n');

    try {
        // Test the test endpoint
        console.log('1. Testing /api/test');
        const testResult = await testAPI('/test');
        console.log(`   Status: ${testResult.status}`);
        console.log(`   Body: ${testResult.body}\n`);

        // Test the modules endpoint (GET)
        console.log('2. Testing /api/modules (GET)');
        const modulesResult = await testAPI('/modules');
        console.log(`   Status: ${modulesResult.status}`);
        console.log(`   Body: ${modulesResult.body}\n`);

        // Test creating a module (POST)
        console.log('3. Testing /api/modules (POST)');
        const createResult = await testAPI('/modules', 'POST', { name: 'Test Module' });
        console.log(`   Status: ${createResult.status}`);
        console.log(`   Body: ${createResult.body}\n`);

    } catch (error) {
        console.error('Error testing API:', error.message);
    }
}

runTests();
