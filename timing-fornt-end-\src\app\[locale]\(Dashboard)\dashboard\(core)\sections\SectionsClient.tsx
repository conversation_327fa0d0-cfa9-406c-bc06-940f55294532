"use client";
import { useState } from "react";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    Dash<PERSON><PERSON>ntA<PERSON>,
    DashConten<PERSON><PERSON><PERSON>,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { <PERSON><PERSON><PERSON>, Trash, User<PERSON>en, Timer } from "lucide-react";
import Link from "next/link";
import CreateSectionDialog from "./CreateSectionDialog";
import { getAllSections, Section, deleteSection } from "@/lib/server/actions/section/sectionActions";
import ConfirmDialog from "@/lib/ui/components/global/Dialog/ConfirmDialog";

interface SectionsClientProps {
    initialSections: Section[];
}

export default function SectionsClient({ initialSections }: SectionsClientProps) {
    const [sections, setSections] = useState<Section[]>(initialSections);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [deleteDialog, setDeleteDialog] = useState<{
        isOpen: boolean;
        section: Section | null;
        isDeleting: boolean;
    }>({
        isOpen: false,
        section: null,
        isDeleting: false
    });

    const refreshSections = async () => {
        setIsRefreshing(true);
        try {
            const updatedSections = await getAllSections();
            setSections(updatedSections);
        } catch (error) {
            console.error('Error refreshing sections:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleDeleteClick = (section: Section) => {
        setDeleteDialog({
            isOpen: true,
            section,
            isDeleting: false
        });
    };

    const handleDeleteConfirm = async () => {
        if (!deleteDialog.section) return;

        setDeleteDialog(prev => ({ ...prev, isDeleting: true }));
        
        try {
            await deleteSection(deleteDialog.section.id);
            // Remove the section from the local state immediately
            setSections(prev => prev.filter(s => s.id !== deleteDialog.section!.id));
            setDeleteDialog({ isOpen: false, section: null, isDeleting: false });
        } catch (error) {
            console.error('Error deleting section:', error);
            setDeleteDialog(prev => ({ ...prev, isDeleting: false }));
        }
    };

    const handleDeleteCancel = () => {
        if (!deleteDialog.isDeleting) {
            setDeleteDialog({ isOpen: false, section: null, isDeleting: false });
        }
    };

    return (
        <DashContent>
            <DashContenTitle>Sections</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem 
                    title="Total Sections" 
                    value={sections.length.toString()} 
                    icon={<UserPen size={80} />} 
                />
            </DashContentStat>
            <DashContentAction>
                <CreateSectionDialog onSuccess={refreshSections} />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Number', 'Year', 'Department', 'Groups', 'Settings']} />
                <tbody>
                    {sections.map((section) => (
                        <TableTr key={section.id}>
                            <TableTdMain value={section.number.toString()} />
                            <TableTd>{section.year?.name || 'No Year'}</TableTd>
                            <TableTd>{section.year?.department?.name || 'No Department'}</TableTd>
                            <TableTd>{section.groups_count || 0}</TableTd>
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/sections/${section.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <button
                                        onClick={() => handleDeleteClick(section)}
                                        className="p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                                        title="Delete section"
                                    >
                                        <Trash className="text-red-600 dark:text-red-400" size={16} />
                                    </button>
                                    <Link href={`/dashboard/sections/timing/${section.id}`}>
                                        <Timer className="text-secondary dark:text-dark-secondary" size={16} />
                                    </Link>
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
            {isRefreshing && (
                <div className="text-center py-4 text-gray-500">
                    Refreshing sections...
                </div>
            )}
            
            <ConfirmDialog
                isOpen={deleteDialog.isOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                title="Delete Section"
                message={`Are you sure you want to delete "Section ${deleteDialog.section?.number}"? This action cannot be undone.`}
                confirmText="Delete"
                cancelText="Cancel"
                isLoading={deleteDialog.isDeleting}
                variant="danger"
            />
        </DashContent>
    );
}
