"use client";
import { useState } from "react";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    Dash<PERSON><PERSON>ntA<PERSON>,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Pencil, Trash, UserPen } from "lucide-react";
import Link from "next/link";
import CreateDepartmentDialog from "./CreateDepartmentDialog";
import { getAllDepartmentsSimple, Department, deleteDepartment } from "@/lib/server/actions/department/DepartmentActions";
import ConfirmDialog from "@/lib/ui/components/global/Dialog/ConfirmDialog";

interface DepartmentsClientProps {
    initialDepartments: Department[];
}

export default function DepartmentsClient({ initialDepartments }: DepartmentsClientProps) {
    const [departments, setDepartments] = useState<Department[]>(initialDepartments);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [deleteDialog, setDeleteDialog] = useState<{
        isOpen: boolean;
        department: Department | null;
        isDeleting: boolean;
    }>({
        isOpen: false,
        department: null,
        isDeleting: false
    });

    const refreshDepartments = async () => {
        setIsRefreshing(true);
        try {
            const updatedDepartments = await getAllDepartmentsSimple();
            setDepartments(updatedDepartments);
        } catch (error) {
            console.error('Error refreshing departments:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleDeleteClick = (department: Department) => {
        setDeleteDialog({
            isOpen: true,
            department,
            isDeleting: false
        });
    };

    const handleDeleteConfirm = async () => {
        if (!deleteDialog.department) return;

        setDeleteDialog(prev => ({ ...prev, isDeleting: true }));

        try {
            await deleteDepartment(deleteDialog.department.id);
            // Remove the department from the local state immediately
            setDepartments(prev => prev.filter(d => d.id !== deleteDialog.department!.id));
            setDeleteDialog({ isOpen: false, department: null, isDeleting: false });
        } catch (error) {
            console.error('Error deleting department:', error);
            setDeleteDialog(prev => ({ ...prev, isDeleting: false }));
        }
    };

    const handleDeleteCancel = () => {
        if (!deleteDialog.isDeleting) {
            setDeleteDialog({ isOpen: false, department: null, isDeleting: false });
        }
    };

    return (
        <DashContent>
            <DashContenTitle>Departments</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem 
                    title="Total Departments" 
                    value={departments.length.toString()} 
                    icon={<UserPen size={80} />} 
                />
            </DashContentStat>
            <DashContentAction>
                <CreateDepartmentDialog onSuccess={refreshDepartments} />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Department Name', 'Settings']} />
                <tbody>
                    {departments.map((department) => (
                        <TableTr key={department.id}>
                            <TableTdMain value={department.name} />
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/departments/${department.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <button
                                        onClick={() => handleDeleteClick(department)}
                                        className="p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                                        title="Delete department"
                                    >
                                        <Trash className="text-red-600 dark:text-red-400" size={16} />
                                    </button>
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
            {isRefreshing && (
                <div className="text-center py-4 text-gray-500">
                    Refreshing departments...
                </div>
            )}

            <ConfirmDialog
                isOpen={deleteDialog.isOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                title="Delete Department"
                message={`Are you sure you want to delete "${deleteDialog.department?.name}"? This action cannot be undone.`}
                confirmText="Delete"
                cancelText="Cancel"
                isLoading={deleteDialog.isDeleting}
                variant="danger"
            />
        </DashContent>
    );
}
