"use client";
import { useState } from "react";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    Dash<PERSON>ontentA<PERSON>,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { <PERSON><PERSON><PERSON>, Trash, User<PERSON>en, Timer } from "lucide-react";
import Link from "next/link";
import CreateGroupDialog from "./CreateGroupDialog";
import { getAllGroups, Group, deleteGroup } from "@/lib/server/actions/group/groupActions";
import ConfirmDialog from "@/lib/ui/components/global/Dialog/ConfirmDialog";

interface GroupsClientProps {
    initialGroups: Group[];
}

export default function GroupsClient({ initialGroups }: GroupsClientProps) {
    const [groups, setGroups] = useState<Group[]>(initialGroups);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [deleteDialog, setDeleteDialog] = useState<{
        isOpen: boolean;
        group: Group | null;
        isDeleting: boolean;
    }>({
        isOpen: false,
        group: null,
        isDeleting: false
    });

    const refreshGroups = async () => {
        setIsRefreshing(true);
        try {
            const updatedGroups = await getAllGroups();
            setGroups(updatedGroups);
        } catch (error) {
            console.error('Error refreshing groups:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleDeleteClick = (group: Group) => {
        setDeleteDialog({
            isOpen: true,
            group,
            isDeleting: false
        });
    };

    const handleDeleteConfirm = async () => {
        if (!deleteDialog.group) return;

        setDeleteDialog(prev => ({ ...prev, isDeleting: true }));
        
        try {
            await deleteGroup(deleteDialog.group.id);
            // Remove the group from the local state immediately
            setGroups(prev => prev.filter(g => g.id !== deleteDialog.group!.id));
            setDeleteDialog({ isOpen: false, group: null, isDeleting: false });
        } catch (error) {
            console.error('Error deleting group:', error);
            setDeleteDialog(prev => ({ ...prev, isDeleting: false }));
        }
    };

    const handleDeleteCancel = () => {
        if (!deleteDialog.isDeleting) {
            setDeleteDialog({ isOpen: false, group: null, isDeleting: false });
        }
    };

    return (
        <DashContent>
            <DashContenTitle>Groups</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem 
                    title="Total Groups" 
                    value={groups.length.toString()} 
                    icon={<UserPen size={80} />} 
                />
            </DashContentStat>
            <DashContentAction>
                <CreateGroupDialog onSuccess={refreshGroups} />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Number', 'Section', 'Year', 'Department', 'Students', 'Settings']} />
                <tbody>
                    {groups.map((group) => (
                        <TableTr key={group.id}>
                            <TableTdMain value={group.number.toString()} />
                            <TableTd>{group.section?.number || 'No Section'}</TableTd>
                            <TableTd>{group.section?.year?.name || 'No Year'}</TableTd>
                            <TableTd>{group.section?.year?.department?.name || 'No Department'}</TableTd>
                            <TableTd>{group.students_count || 0}</TableTd>
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/groups/${group.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <button
                                        onClick={() => handleDeleteClick(group)}
                                        className="p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                                        title="Delete group"
                                    >
                                        <Trash className="text-red-600 dark:text-red-400" size={16} />
                                    </button>
                                    <Link href={`/dashboard/groups/timing/${group.id}`}>
                                        <Timer className="text-secondary dark:text-dark-secondary" size={16} />
                                    </Link>
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
            {isRefreshing && (
                <div className="text-center py-4 text-gray-500">
                    Refreshing groups...
                </div>
            )}
            
            <ConfirmDialog
                isOpen={deleteDialog.isOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                title="Delete Group"
                message={`Are you sure you want to delete "Group ${deleteDialog.group?.number}"? This action cannot be undone.`}
                confirmText="Delete"
                cancelText="Cancel"
                isLoading={deleteDialog.isDeleting}
                variant="danger"
            />
        </DashContent>
    );
}
