"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(core)/years/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/years/YearsClient.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/[locale]/(Dashboard)/dashboard/(core)/years/YearsClient.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ YearsClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ui/components/local/Dashboard/DashCrudContent */ \"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/DashCrudContent.tsx\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _CreateYearDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateYearDialog */ \"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/years/CreateYearDialog.tsx\");\n/* harmony import */ var _lib_server_actions_year_yearActions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/server/actions/year/yearActions */ \"(app-pages-browser)/./src/lib/server/actions/year/yearActions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction YearsClient(param) {\n    let { initialYears } = param;\n    _s();\n    const [years, setYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialYears);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteDialog, setDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        year: null,\n        isDeleting: false\n    });\n    const refreshYears = async ()=>{\n        setIsRefreshing(true);\n        try {\n            const updatedYears = await (0,_lib_server_actions_year_yearActions__WEBPACK_IMPORTED_MODULE_5__.getAllYears)();\n            setYears(updatedYears);\n        } catch (error) {\n            console.error('Error refreshing years:', error);\n        } finally{\n            setIsRefreshing(false);\n        }\n    };\n    const handleDeleteClick = (year)=>{\n        setDeleteDialog({\n            isOpen: true,\n            year,\n            isDeleting: false\n        });\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!deleteDialog.year) return;\n        setDeleteDialog((prev)=>({\n                ...prev,\n                isDeleting: true\n            }));\n        try {\n            await (0,_lib_server_actions_year_yearActions__WEBPACK_IMPORTED_MODULE_5__.deleteYear)(deleteDialog.year.id);\n            // Remove the year from the local state immediately\n            setYears((prev)=>prev.filter((y)=>y.id !== deleteDialog.year.id));\n            setDeleteDialog({\n                isOpen: false,\n                year: null,\n                isDeleting: false\n            });\n        } catch (error) {\n            console.error('Error deleting year:', error);\n            setDeleteDialog((prev)=>({\n                    ...prev,\n                    isDeleting: false\n                }));\n        }\n    };\n    const handleDeleteCancel = ()=>{\n        if (!deleteDialog.isDeleting) {\n            setDeleteDialog({\n                isOpen: false,\n                year: null,\n                isDeleting: false\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContent, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContenTitle, {\n                children: \"Years\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                lineNumber: 82,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentStat, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentStatItem, {\n                    title: \"Total Years\",\n                    value: years.length.toString(),\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 80\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 27\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                lineNumber: 83,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentAction, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateYearDialog__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onSuccess: refreshYears\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                lineNumber: 90,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentTable, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableThead, {\n                        list: [\n                            'Year Name',\n                            'Department',\n                            'Settings'\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: years.map((year)=>{\n                            var _year_department;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTdMain, {\n                                        value: year.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTd, {\n                                        children: ((_year_department = year.department) === null || _year_department === void 0 ? void 0 : _year_department.name) || 'N/A'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTd, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/years/\".concat(year.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"text-green-700 dark:text-green-400\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteClick(year),\n                                                    className: \"p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded\",\n                                                    title: \"Delete year\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"text-red-600 dark:text-red-400\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, year.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 25\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                lineNumber: 93,\n                columnNumber: 13\n            }, this),\n            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4 text-gray-500\",\n                children: \"Refreshing years...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n                lineNumber: 119,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\years\\\\YearsClient.tsx\",\n        lineNumber: 81,\n        columnNumber: 9\n    }, this);\n}\n_s(YearsClient, \"34gkgbpuRViVDDAd47vh2kAXEn4=\");\n_c = YearsClient;\nvar _c;\n$RefreshReg$(_c, \"YearsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/years/YearsClient.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ac6e27112e2b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWVcXEZpbmFsIHByb2plY3RcXHRpbWluZy1mb3JudC1lbmQtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhYzZlMjcxMTJlMmJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

});