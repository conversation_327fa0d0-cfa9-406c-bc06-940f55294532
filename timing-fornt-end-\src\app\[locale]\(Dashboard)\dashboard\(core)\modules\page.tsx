import { getAllModules } from "@/lib/server/actions/module/moduleActions";
import ModulesClient from "./ModulesClient";

export default async function ModulesPage() {
    let modules = [];

    try {
        modules = await getAllModules();
    } catch (error) {
        console.error('Error fetching modules:', error);
        modules = [];
    }

    return <ModulesClient initialModules={modules} />;
}
