<?php

// Simple test script to check if <PERSON><PERSON> is working
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

echo "Testing Laravel application...\n";

try {
    // Test database connection
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=time_table', 'root', '');
    echo "✅ Database connection: OK\n";
} catch (Exception $e) {
    echo "❌ Database connection: FAILED - " . $e->getMessage() . "\n";
}

// Test if routes are loaded
try {
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    echo "✅ Laravel kernel: OK\n";
} catch (Exception $e) {
    echo "❌ Laravel kernel: FAILED - " . $e->getMessage() . "\n";
}

echo "Test completed.\n";
