"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(core)/modules/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/triangle-alert.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ TriangleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.510.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n            key: \"wmoenq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 9v4\",\n            key: \"juzpu7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n];\nconst TriangleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"triangle-alert\", __iconNode);\n //# sourceMappingURL=triangle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/ModulesClient.tsx":
/*!*********************************************************************************!*\
  !*** ./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/ModulesClient.tsx ***!
  \*********************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModulesClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ui/components/local/Dashboard/DashCrudContent */ \"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/DashCrudContent.tsx\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Timer,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Timer,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Timer,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Timer,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _CreateModuleDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateModuleDialog */ \"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/CreateModuleDialog.tsx\");\n/* harmony import */ var _lib_server_actions_module_moduleActions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/server/actions/module/moduleActions */ \"(app-pages-browser)/./src/lib/server/actions/module/moduleActions.ts\");\n/* harmony import */ var _lib_ui_components_global_Dialog_ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/ui/components/global/Dialog/ConfirmDialog */ \"(app-pages-browser)/./src/lib/ui/components/global/Dialog/ConfirmDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ModulesClient(param) {\n    let { initialModules } = param;\n    var _deleteDialog_module;\n    _s();\n    const [modules, setModules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialModules);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteDialog, setDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        module: null,\n        isDeleting: false\n    });\n    const refreshModules = async ()=>{\n        setIsRefreshing(true);\n        try {\n            const updatedModules = await (0,_lib_server_actions_module_moduleActions__WEBPACK_IMPORTED_MODULE_5__.getAllModules)();\n            setModules(updatedModules);\n        } catch (error) {\n            console.error('Error refreshing modules:', error);\n        } finally{\n            setIsRefreshing(false);\n        }\n    };\n    const handleDeleteClick = (module)=>{\n        setDeleteDialog({\n            isOpen: true,\n            module,\n            isDeleting: false\n        });\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!deleteDialog.module) return;\n        setDeleteDialog((prev)=>({\n                ...prev,\n                isDeleting: true\n            }));\n        try {\n            await (0,_lib_server_actions_module_moduleActions__WEBPACK_IMPORTED_MODULE_5__.deleteModule)(deleteDialog.module.id);\n            // Remove the module from the local state immediately\n            setModules((prev)=>prev.filter((m)=>m.id !== deleteDialog.module.id));\n            setDeleteDialog({\n                isOpen: false,\n                module: null,\n                isDeleting: false\n            });\n        } catch (error) {\n            console.error('Error deleting module:', error);\n            setDeleteDialog((prev)=>({\n                    ...prev,\n                    isDeleting: false\n                }));\n        // You could add a toast notification here for error feedback\n        }\n    };\n    const handleDeleteCancel = ()=>{\n        if (!deleteDialog.isDeleting) {\n            setDeleteDialog({\n                isOpen: false,\n                module: null,\n                isDeleting: false\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContent, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContenTitle, {\n                children: \"Modules\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 83,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentStat, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentStatItem, {\n                    title: \"Total Modules\",\n                    value: modules.length.toString(),\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 80\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 27\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentAction, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateModuleDialog__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onSuccess: refreshModules\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 91,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentTable, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableThead, {\n                        list: [\n                            'Module Name',\n                            'Settings'\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTdMain, {\n                                        value: module.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTd, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/modules/\".concat(module.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"text-green-700 dark:text-green-400\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteClick(module),\n                                                    className: \"p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded\",\n                                                    title: \"Delete module\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"text-red-600 dark:text-red-400\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/modules/\".concat(module.id, \"/schedule\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Timer_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"text-secondary dark:text-dark-secondary\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, module.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 25\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 94,\n                columnNumber: 13\n            }, this),\n            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4 text-gray-500\",\n                children: \"Refreshing modules...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 122,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_global_Dialog_ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: deleteDialog.isOpen,\n                onClose: handleDeleteCancel,\n                onConfirm: handleDeleteConfirm,\n                title: \"Delete Module\",\n                message: 'Are you sure you want to delete \"'.concat((_deleteDialog_module = deleteDialog.module) === null || _deleteDialog_module === void 0 ? void 0 : _deleteDialog_module.name, '\"? This action cannot be undone.'),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                isLoading: deleteDialog.isDeleting,\n                variant: \"danger\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n                lineNumber: 127,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\modules\\\\ModulesClient.tsx\",\n        lineNumber: 82,\n        columnNumber: 9\n    }, this);\n}\n_s(ModulesClient, \"ksLm/+ueBCq0amAwjYM0aCvQm/M=\");\n_c = ModulesClient;\nvar _c;\n$RefreshReg$(_c, \"ModulesClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/modules/ModulesClient.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d317bf5856ad\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWVcXEZpbmFsIHByb2plY3RcXHRpbWluZy1mb3JudC1lbmQtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkMzE3YmY1ODU2YWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/ui/components/global/Dialog/ConfirmDialog.tsx":
/*!***************************************************************!*\
  !*** ./src/lib/ui/components/global/Dialog/ConfirmDialog.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfirmDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Buttons_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Buttons/Button */ \"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ConfirmDialog(param) {\n    let { isOpen, onClose, onConfirm, title, message, confirmText = \"Confirm\", cancelText = \"Cancel\", isLoading = false, variant = \"danger\" } = param;\n    _s();\n    const dialogRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfirmDialog.useEffect\": ()=>{\n            const handleEscape = {\n                \"ConfirmDialog.useEffect.handleEscape\": (e)=>{\n                    if (e.key === \"Escape\" && !isLoading) {\n                        onClose();\n                    }\n                }\n            }[\"ConfirmDialog.useEffect.handleEscape\"];\n            const handleClickOutside = {\n                \"ConfirmDialog.useEffect.handleClickOutside\": (e)=>{\n                    if (dialogRef.current && !dialogRef.current.contains(e.target) && !isLoading) {\n                        onClose();\n                    }\n                }\n            }[\"ConfirmDialog.useEffect.handleClickOutside\"];\n            if (isOpen) {\n                document.addEventListener(\"keydown\", handleEscape);\n                document.addEventListener(\"mousedown\", handleClickOutside);\n                document.body.style.overflow = \"hidden\";\n            }\n            return ({\n                \"ConfirmDialog.useEffect\": ()=>{\n                    document.removeEventListener(\"keydown\", handleEscape);\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                    document.body.style.overflow = \"unset\";\n                }\n            })[\"ConfirmDialog.useEffect\"];\n        }\n    }[\"ConfirmDialog.useEffect\"], [\n        isOpen,\n        onClose,\n        isLoading\n    ]);\n    if (!isOpen) return null;\n    const getVariantStyles = ()=>{\n        switch(variant){\n            case \"danger\":\n                return {\n                    icon: \"text-red-500\",\n                    confirmButton: \"bg-red-600 hover:bg-red-700 text-white\"\n                };\n            case \"warning\":\n                return {\n                    icon: \"text-yellow-500\",\n                    confirmButton: \"bg-yellow-600 hover:bg-yellow-700 text-white\"\n                };\n            default:\n                return {\n                    icon: \"text-blue-500\",\n                    confirmButton: \"bg-blue-600 hover:bg-blue-700 text-white\"\n                };\n        }\n    };\n    const styles = getVariantStyles();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: dialogRef,\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"\".concat(styles.icon),\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 21\n                        }, this),\n                        !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-300 mb-6\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 justify-end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Buttons_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    mode: \"outlined\",\n                                    onClick: onClose,\n                                    disabled: isLoading,\n                                    children: cancelText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onConfirm,\n                                    disabled: isLoading,\n                                    className: \"px-4 py-2 rounded-full font-medium transition-colors disabled:opacity-50 \".concat(styles.confirmButton),\n                                    children: isLoading ? \"Deleting...\" : confirmText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n            lineNumber: 83,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\lib\\\\ui\\\\components\\\\global\\\\Dialog\\\\ConfirmDialog.tsx\",\n        lineNumber: 82,\n        columnNumber: 9\n    }, this);\n}\n_s(ConfirmDialog, \"9PxoOcTjzEwd023cmhgaBdjzFyE=\");\n_c = ConfirmDialog;\nvar _c;\n$RefreshReg$(_c, \"ConfirmDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ui/components/global/Dialog/ConfirmDialog.tsx\n"));

/***/ })

});