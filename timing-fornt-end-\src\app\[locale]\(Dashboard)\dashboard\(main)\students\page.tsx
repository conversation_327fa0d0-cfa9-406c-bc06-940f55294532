import { getAllStudents } from "@/lib/server/actions/student/studentActions";
import StudentsClient from "./StudentsClient";

export default async function StudentsPage() {
    let students = [];

    try {
        students = await getAllStudents();
    } catch (error) {
        console.error('Error fetching students:', error);
        students = [];
    }

    return <StudentsClient initialStudents={students} />;
}