"use client";
import { useState } from "react";
import {
    <PERSON><PERSON><PERSON>nt,
    Dash<PERSON>ontentA<PERSON>,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { <PERSON><PERSON><PERSON>, Trash, User<PERSON>en, Timer } from "lucide-react";
import Link from "next/link";
import CreateModuleDialog from "./CreateModuleDialog";
import { getAllModules, Module, deleteModule } from "@/lib/server/actions/module/moduleActions";
import ConfirmDialog from "@/lib/ui/components/global/Dialog/ConfirmDialog";

interface ModulesClientProps {
    initialModules: Module[];
}

export default function ModulesClient({ initialModules }: ModulesClientProps) {
    const [modules, setModules] = useState<Module[]>(initialModules);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [deleteDialog, setDeleteDialog] = useState<{
        isOpen: boolean;
        module: Module | null;
        isDeleting: boolean;
    }>({
        isOpen: false,
        module: null,
        isDeleting: false
    });

    const refreshModules = async () => {
        setIsRefreshing(true);
        try {
            const updatedModules = await getAllModules();
            setModules(updatedModules);
        } catch (error) {
            console.error('Error refreshing modules:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleDeleteClick = (module: Module) => {
        setDeleteDialog({
            isOpen: true,
            module,
            isDeleting: false
        });
    };

    const handleDeleteConfirm = async () => {
        if (!deleteDialog.module) return;

        setDeleteDialog(prev => ({ ...prev, isDeleting: true }));

        try {
            await deleteModule(deleteDialog.module.id);
            // Remove the module from the local state immediately
            setModules(prev => prev.filter(m => m.id !== deleteDialog.module!.id));
            setDeleteDialog({ isOpen: false, module: null, isDeleting: false });
        } catch (error) {
            console.error('Error deleting module:', error);
            setDeleteDialog(prev => ({ ...prev, isDeleting: false }));
            // You could add a toast notification here for error feedback
        }
    };

    const handleDeleteCancel = () => {
        if (!deleteDialog.isDeleting) {
            setDeleteDialog({ isOpen: false, module: null, isDeleting: false });
        }
    };

    return (
        <DashContent>
            <DashContenTitle>Modules</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem 
                    title="Total Modules" 
                    value={modules.length.toString()} 
                    icon={<UserPen size={80} />} 
                />
            </DashContentStat>
            <DashContentAction>
                <CreateModuleDialog onSuccess={refreshModules} />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Module Name', 'Settings']} />
                <tbody>
                    {modules.map((module) => (
                        <TableTr key={module.id}>
                            <TableTdMain value={module.name} />
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/modules/${module.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <button
                                        onClick={() => handleDeleteClick(module)}
                                        className="p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                                        title="Delete module"
                                    >
                                        <Trash className="text-red-600 dark:text-red-400" size={16} />
                                    </button>
                                    <Link href={`/dashboard/modules/${module.id}/schedule`}>
                                        <Timer className="text-secondary dark:text-dark-secondary" size={16} />
                                    </Link>
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
            {isRefreshing && (
                <div className="text-center py-4 text-gray-500">
                    Refreshing modules...
                </div>
            )}

            <ConfirmDialog
                isOpen={deleteDialog.isOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                title="Delete Module"
                message={`Are you sure you want to delete "${deleteDialog.module?.name}"? This action cannot be undone.`}
                confirmText="Delete"
                cancelText="Cancel"
                isLoading={deleteDialog.isDeleting}
                variant="danger"
            />
        </DashContent>
    );
}
