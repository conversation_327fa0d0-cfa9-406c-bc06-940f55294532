"use client";
import { useState } from "react";
import {
    Dash<PERSON><PERSON><PERSON>,
    Dash<PERSON><PERSON>ntA<PERSON>,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Pencil, Trash, UserPen } from "lucide-react";
import Link from "next/link";
import CreateYearDialog from "./CreateYearDialog";
import { getAllYears, YearWithDepartment, deleteYear } from "@/lib/server/actions/year/yearActions";
import ConfirmDialog from "@/lib/ui/components/global/Dialog/ConfirmDialog";

interface YearsClientProps {
    initialYears: YearWithDepartment[];
}

export default function YearsClient({ initialYears }: YearsClientProps) {
    const [years, setYears] = useState<YearWithDepartment[]>(initialYears);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [deleteDialog, setDeleteDialog] = useState<{
        isOpen: boolean;
        year: YearWithDepartment | null;
        isDeleting: boolean;
    }>({
        isOpen: false,
        year: null,
        isDeleting: false
    });

    const refreshYears = async () => {
        setIsRefreshing(true);
        try {
            const updatedYears = await getAllYears();
            setYears(updatedYears);
        } catch (error) {
            console.error('Error refreshing years:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleDeleteClick = (year: YearWithDepartment) => {
        setDeleteDialog({
            isOpen: true,
            year,
            isDeleting: false
        });
    };

    const handleDeleteConfirm = async () => {
        if (!deleteDialog.year) return;

        setDeleteDialog(prev => ({ ...prev, isDeleting: true }));

        try {
            await deleteYear(deleteDialog.year.id);
            // Remove the year from the local state immediately
            setYears(prev => prev.filter(y => y.id !== deleteDialog.year!.id));
            setDeleteDialog({ isOpen: false, year: null, isDeleting: false });
        } catch (error) {
            console.error('Error deleting year:', error);
            setDeleteDialog(prev => ({ ...prev, isDeleting: false }));
        }
    };

    const handleDeleteCancel = () => {
        if (!deleteDialog.isDeleting) {
            setDeleteDialog({ isOpen: false, year: null, isDeleting: false });
        }
    };

    return (
        <DashContent>
            <DashContenTitle>Years</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem 
                    title="Total Years" 
                    value={years.length.toString()} 
                    icon={<UserPen size={80} />} 
                />
            </DashContentStat>
            <DashContentAction>
                <CreateYearDialog onSuccess={refreshYears} />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Year Name', 'Department', 'Settings']} />
                <tbody>
                    {years.map((year) => (
                        <TableTr key={year.id}>
                            <TableTdMain value={year.name} />
                            <TableTd>{year.department?.name || 'N/A'}</TableTd>
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/years/${year.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <button
                                        onClick={() => handleDeleteClick(year)}
                                        className="p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                                        title="Delete year"
                                    >
                                        <Trash className="text-red-600 dark:text-red-400" size={16} />
                                    </button>
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
            {isRefreshing && (
                <div className="text-center py-4 text-gray-500">
                    Refreshing years...
                </div>
            )}

            <ConfirmDialog
                isOpen={deleteDialog.isOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                title="Delete Year"
                message={`Are you sure you want to delete "${deleteDialog.year?.name}"? This action cannot be undone.`}
                confirmText="Delete"
                cancelText="Cancel"
                isLoading={deleteDialog.isDeleting}
                variant="danger"
            />
        </DashContent>
    );
}
