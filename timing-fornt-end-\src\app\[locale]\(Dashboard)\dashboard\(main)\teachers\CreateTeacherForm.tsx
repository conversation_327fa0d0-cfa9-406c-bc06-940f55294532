"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CheckCircle2, AlertCircle } from "lucide-react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import { createTeacher } from "@/lib/server/actions/teacher/teacherActions";

const createTeacherSchema = z.object({
    name: z.string()
        .min(1, "Name is required")
        .regex(/^[A-Z]/, "First letter must be capital")
        .regex(/^[A-Z][a-z]*$/, "Only letters are allowed"),
    last: z.string()
        .min(1, "Last name is required")
        .regex(/^[A-Z]/, "First letter must be capital")
        .regex(/^[A-Z][a-z]*$/, "Only letters are allowed"),
    date_of_birth: z.string().min(1, "Date of birth is required"),
    grade: z.string().optional(),
    research_field: z.string().optional(),
});

type CreateTeacherFormData = z.infer<typeof createTeacherSchema>;

interface CreateTeacherFormProps {
    onSuccess?: () => void;
}

export default function CreateTeacherForm({ onSuccess }: CreateTeacherFormProps) {
    const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
    } = useForm<CreateTeacherFormData>({
        resolver: zodResolver(createTeacherSchema),
    });

    const onSubmit = async (data: CreateTeacherFormData) => {
        try {
            setSubmitStatus('idle');
            await createTeacher({
                name: data.name,
                last: data.last,
                date_of_birth: data.date_of_birth,
                grade: data.grade,
                research_field: data.research_field,
            });
            setSubmitStatus('success');
            setTimeout(() => {
                onSuccess?.();
            }, 1000);
        } catch (error) {
            console.error('Error creating teacher:', error);
            setSubmitStatus('error');
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
            {submitStatus === 'success' && (
                <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                    <CheckCircle2 size={20} />
                    <span>Teacher created successfully!</span>
                </div>
            )}
            {submitStatus === 'error' && (
                <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                    <AlertCircle size={20} />
                    <span>Error creating teacher. Please try again.</span>
                </div>
            )}

            <Input
                label="name"
                title="Name"
                placeholder="Enter name (First letter capital)"
                error={errors.name?.message}
                register={register}
            />
            <Input
                label="last"
                title="Last Name"
                placeholder="Enter last name (First letter capital)"
                error={errors.last?.message}
                register={register}
            />
            <Input
                label="date_of_birth"
                title="Date of Birth"
                type="date"
                error={errors.date_of_birth?.message}
                register={register}
            />
            <Input
                label="grade"
                title="Grade (Optional)"
                placeholder="Enter grade (e.g., Professor, Assistant Professor)"
                error={errors.grade?.message}
                register={register}
            />
            <Input
                label="research_field"
                title="Research Field (Optional)"
                placeholder="Enter research field"
                error={errors.research_field?.message}
                register={register}
            />

            <Button
                type="submit"
                mode="filled"
                disabled={isSubmitting}
            >
                {isSubmitting ? "Creating..." : "Create Teacher"}
            </Button>
        </form>
    );
}
