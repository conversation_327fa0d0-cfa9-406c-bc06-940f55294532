"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateYearForm from "@/lib/ui/forms/year/CreateYearForm";
import { Plus } from "lucide-react";

interface CreateYearDialogProps {
    onSuccess?: () => void;
}

export default function CreateYearDialog({ onSuccess }: CreateYearDialogProps) {
    const [open, setOpen] = useState(false);

    const handleCloseDialog = () => {
        setOpen(false);
        onSuccess?.();
    };

    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Year
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Year">
                <CreateYearForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
}
