"use client";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CheckCircle2, AlertCircle } from "lucide-react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import { SimpleSelect } from "@/lib/ui/components/global/Inputs/SimpleSelect";
import { createStudent } from "@/lib/server/actions/student/studentActions";
import { getAllDepartments } from "@/lib/server/actions/department/DepartmentActions";
import { Department, Section, Year } from "@/lib/server/types/departments/allDepartments";
import { Group } from "@/lib/server/types/student/student";

const createStudentSchema = z.object({
    name: z.string()
        .min(1, "Name is required")
        .regex(/^[A-Z]/, "First letter must be capital")
        .regex(/^[A-Z][a-z]*$/, "Only letters are allowed"),
    last: z.string()
        .min(1, "Last name is required")
        .regex(/^[A-Z]/, "First letter must be capital")
        .regex(/^[A-Z][a-z]*$/, "Only letters are allowed"),
    date_of_birth: z.string().min(1, "Date of birth is required"),
    inscreption_number: z.string()
        .min(1, "Inscription number is required")
        .regex(/^\d+$/, "Only numbers are allowed"),
    group_id: z.string().min(1, "Group is required"),
    department_id: z.string().min(1, "Department is required"),
    year_id: z.string().min(1, "Year is required"),
    section_id: z.string().min(1, "Section is required"),
});

type CreateStudentFormData = z.infer<typeof createStudentSchema>;

interface CreateStudentFormProps {
    onSuccess?: () => void;
}

export default function CreateStudentForm({ onSuccess }: CreateStudentFormProps) {
    const [departments, setDepartments] = useState<Department[]>([]);
    const [years, setYears] = useState<Year[]>([]);
    const [sections, setSections] = useState<Section[]>([]);
    const [groups, setGroups] = useState<Group[]>([]);
    const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

    const {
        register,
        handleSubmit,
        watch,
        setValue,
        formState: { errors, isSubmitting },
    } = useForm<CreateStudentFormData>({
        resolver: zodResolver(createStudentSchema),
    });

    useEffect(() => {
        const fetchDepartments = async () => {
            try {
                const data = await getAllDepartments();
                setDepartments(data.departments);
            } catch (error) {
                console.error('Error fetching departments:', error);
            }
        };
        fetchDepartments();
    }, []);

    const onSubmit = async (data: CreateStudentFormData) => {
        try {
            setSubmitStatus('idle');
            await createStudent({
                name: data.name,
                last: data.last,
                date_of_birth: data.date_of_birth,
                inscreption_number: data.inscreption_number,
                group_id: Number(data.group_id),
            });
            setSubmitStatus('success');
            setTimeout(() => {
                onSuccess?.();
            }, 1000);
        } catch (error) {
            console.error('Error creating student:', error);
            setSubmitStatus('error');
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
            {submitStatus === 'success' && (
                <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                    <CheckCircle2 size={20} />
                    <span>Student created successfully!</span>
                </div>
            )}
            {submitStatus === 'error' && (
                <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                    <AlertCircle size={20} />
                    <span>Error creating student. Please try again.</span>
                </div>
            )}

            <Input
                label="name"
                title="Name"
                placeholder="Enter name (First letter capital)"
                error={errors.name?.message}
                register={register}
            />
            <Input
                label="last"
                title="Last Name"
                placeholder="Enter last name (First letter capital)"
                error={errors.last?.message}
                register={register}
            />
            <Input
                label="date_of_birth"
                title="Date of Birth"
                type="date"
                error={errors.date_of_birth?.message}
                register={register}
            />
            <Input
                label="inscreption_number"
                title="Inscription Number"
                placeholder="Enter inscription number"
                error={errors.inscreption_number?.message}
                register={register}
            />

            <SimpleSelect
                title="Department"
                label="department_id"
                register={register("department_id")}
                error={errors.department_id?.message}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                    const departmentId = e.target.value;
                    setValue("department_id", departmentId);
                    if (departmentId) {
                        const selectedDepartment = departments.find(dept => dept.id === +departmentId);
                        setYears(selectedDepartment ? selectedDepartment.years : []);
                    } else {
                        setYears([]);
                    }
                    setSections([]);
                    setGroups([]);
                }}
            >
                <option value="">Select Department</option>
                {departments.map((department) => (
                    <option key={department.id} value={department.id}>
                        {department.name}
                    </option>
                ))}
            </SimpleSelect>

            {watch('department_id') && (
                <SimpleSelect
                    title="Year"
                    label="year_id"
                    register={register("year_id")}
                    error={errors.year_id?.message}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                        const yearId = e.target.value;
                        setValue("year_id", yearId);
                        if (yearId) {
                            const selectedYear = years.find(year => year.id === +yearId);
                            setSections(selectedYear ? selectedYear.sections : []);
                        } else {
                            setSections([]);
                        }
                        setGroups([]);
                    }}
                >
                    <option value="">Select Year</option>
                    {years.map((year) => (
                        <option key={year.id} value={year.id}>
                            {year.name}
                        </option>
                    ))}
                </SimpleSelect>
            )}

            {watch('year_id') && (
                <SimpleSelect
                    title="Section"
                    label="section_id"
                    register={register("section_id")}
                    error={errors.section_id?.message}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                        const sectionId = e.target.value;
                        setValue("section_id", sectionId);
                        if (sectionId) {
                            const selectedSection = sections.find(sec => sec.id === +sectionId);
                            setGroups(selectedSection ? selectedSection.groups : []);
                        } else {
                            setGroups([]);
                        }
                    }}
                >
                    <option value="">Select Section</option>
                    {sections.map((section) => (
                        <option key={section.id} value={section.id}>
                            Section {section.number}
                        </option>
                    ))}
                </SimpleSelect>
            )}

            {watch('section_id') && (
                <SimpleSelect
                    title="Group"
                    label="group_id"
                    register={register("group_id")}
                    error={errors.group_id?.message}
                >
                    <option value="">Select Group</option>
                    {groups.map((group) => (
                        <option key={group.id} value={group.id}>
                            Group {group.number}
                        </option>
                    ))}
                </SimpleSelect>
            )}

            <Button
                type="submit"
                mode="filled"
                disabled={isSubmitting}
            >
                {isSubmitting ? "Creating..." : "Create Student"}
            </Button>
        </form>
    );
}
