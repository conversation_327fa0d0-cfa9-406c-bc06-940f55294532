"use client";
import { useState } from "react";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    Dash<PERSON><PERSON>ntA<PERSON>,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Pencil, Trash, UserPen } from "lucide-react";
import Link from "next/link";
import CreateTeacherDialog from "./CreateTeacherDialog";
import { getAllTeachers, Teacher, deleteTeacher } from "@/lib/server/actions/teacher/teacherActions";
import ConfirmDialog from "@/lib/ui/components/global/Dialog/ConfirmDialog";

interface TeachersClientProps {
    initialTeachers: Teacher[];
}

export default function TeachersClient({ initialTeachers }: TeachersClientProps) {
    const [teachers, setTeachers] = useState<Teacher[]>(initialTeachers);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [deleteDialog, setDeleteDialog] = useState<{
        isOpen: boolean;
        teacher: Teacher | null;
        isDeleting: boolean;
    }>({
        isOpen: false,
        teacher: null,
        isDeleting: false
    });

    const refreshTeachers = async () => {
        setIsRefreshing(true);
        try {
            const updatedTeachers = await getAllTeachers();
            setTeachers(updatedTeachers);
        } catch (error) {
            console.error('Error refreshing teachers:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleDeleteClick = (teacher: Teacher) => {
        setDeleteDialog({
            isOpen: true,
            teacher,
            isDeleting: false
        });
    };

    const handleDeleteConfirm = async () => {
        if (!deleteDialog.teacher) return;

        setDeleteDialog(prev => ({ ...prev, isDeleting: true }));
        
        try {
            await deleteTeacher(deleteDialog.teacher.id);
            // Remove the teacher from the local state immediately
            setTeachers(prev => prev.filter(t => t.id !== deleteDialog.teacher!.id));
            setDeleteDialog({ isOpen: false, teacher: null, isDeleting: false });
        } catch (error) {
            console.error('Error deleting teacher:', error);
            setDeleteDialog(prev => ({ ...prev, isDeleting: false }));
        }
    };

    const handleDeleteCancel = () => {
        if (!deleteDialog.isDeleting) {
            setDeleteDialog({ isOpen: false, teacher: null, isDeleting: false });
        }
    };

    return (
        <DashContent>
            <DashContenTitle>Teachers</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem 
                    title="Total Teachers" 
                    value={teachers.length.toString()} 
                    icon={<UserPen size={80} />} 
                />
            </DashContentStat>
            <DashContentAction>
                <CreateTeacherDialog onSuccess={refreshTeachers} />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Name', 'Username', 'Grade', 'Research Field', 'Date of Birth', 'Settings']} />
                <tbody>
                    {teachers.map((teacher) => (
                        <TableTr key={teacher.id}>
                            <TableTdMain value={`${teacher.name} ${teacher.last}`} />
                            <TableTd>{teacher.username}</TableTd>
                            <TableTd>{teacher.grade || '—'}</TableTd>
                            <TableTd>{teacher.research_field || '—'}</TableTd>
                            <TableTd>{teacher.date_of_birth}</TableTd>
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/teachers/${teacher.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <button
                                        onClick={() => handleDeleteClick(teacher)}
                                        className="p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                                        title="Delete teacher"
                                    >
                                        <Trash className="text-red-600 dark:text-red-400" size={16} />
                                    </button>
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
            {isRefreshing && (
                <div className="text-center py-4 text-gray-500">
                    Refreshing teachers...
                </div>
            )}
            
            <ConfirmDialog
                isOpen={deleteDialog.isOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                title="Delete Teacher"
                message={`Are you sure you want to delete "${deleteDialog.teacher?.name} ${deleteDialog.teacher?.last}"? This action cannot be undone.`}
                confirmText="Delete"
                cancelText="Cancel"
                isLoading={deleteDialog.isDeleting}
                variant="danger"
            />
        </DashContent>
    );
}
