"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateModuleForm from "@/lib/ui/forms/module/CreateModuleForm";
import { Plus } from "lucide-react";

interface CreateModuleDialogProps {
    onSuccess?: () => void;
}

export default function CreateModuleDialog({ onSuccess }: CreateModuleDialogProps) {
    const [open, setOpen] = useState(false);

    const handleOpenDialog = () => {
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
        onSuccess?.();
    };

    return (
        <>
            <Button mode="filled" icon={<Plus size={24} />} onClick={handleOpenDialog}>
                Create Module
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Module">
                <CreateModuleForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
}
