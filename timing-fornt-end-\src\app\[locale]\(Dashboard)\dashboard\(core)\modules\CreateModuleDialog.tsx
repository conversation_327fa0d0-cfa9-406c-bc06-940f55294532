"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateModuleForm from "@/lib/ui/forms/module/CreateModuleForm";
import { Plus } from "lucide-react";

export default function CreateModuleDialog() {
    const [open, setOpen] = useState(false);

    const handleOpenDialog = () => {
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    return (
        <>
            <Button mode="filled" icon={<Plus size={24} />} onClick={handleOpenDialog}>
                Create Module
            </Button>
            <Dialog isOpen={open} onClose={handleCloseDialog} title="Create Module">
                <CreateModuleForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
}
