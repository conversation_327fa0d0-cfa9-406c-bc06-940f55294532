<?php

namespace App\Http\Controllers\Api\Core;

use App\Http\Controllers\Controller;
use App\Models\Api\Core\Module;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ModulesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $modules = Module::orderBy('created_at', 'desc')->paginate(10);
        return response()->json($modules);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:modules,name',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $module = Module::create([
                'name' => $request->name,
            ]);

            return response()->json([
                'message' => 'Module created successfully',
                'module' => $module
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create module',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Module $module)
    {
        return response()->json([
            'message' => 'Module fetched successfully',
            'module' => $module
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Module $module)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:modules,name,' . $module->id,
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $module->update([
                'name' => $request->name,
            ]);

            return response()->json([
                'message' => 'Module updated successfully',
                'module' => $module
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update module',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Module $module)
    {
        try {
            $module->delete();

            return response()->json([
                'message' => 'Module deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete module',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
