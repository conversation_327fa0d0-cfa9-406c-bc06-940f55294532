'use server'

import { Section, SectionErrorResponse } from '@/lib/server/types/section/section'
import axiosInstance from '@/lib/server/tools/axios'
import { revalidatePath } from 'next/cache'

export async function createSection(sectionData: Section): Promise<Section | SectionErrorResponse> {
    try {
        const { data } = await axiosInstance.post<Section>(
            `/sections`,
            sectionData
        )
        revalidatePath('/dashboard/sections')
        return data
    } catch (error: any) {
        if (error.response?.data) {
            return error.response.data as SectionErrorResponse
        }
        throw error
    }
}

export async function updateSection(id: number, sectionData: Partial<Section>): Promise<Section | SectionErrorResponse> {
    try {
        const { data } = await axiosInstance.put<Section>(
            `/sections/${id}`,
            sectionData
        )
        revalidatePath('/dashboard/sections')
        return data
    } catch (error: any) {
        if (error.response?.data) {
            return error.response.data as SectionErrorResponse
        }
        throw error
    }
}

export async function deleteSection(id: number): Promise<{ success: boolean }> {
    try {
        await axiosInstance.delete(`/sections/${id}`)
        revalidatePath('/dashboard/sections')
        return { success: true }
    } catch (error) {
        console.error('Error deleting section:', error)
        throw error
    }
}

export async function createSectionKey(id: number): Promise<{ success: boolean; key?: string }> {
    try {
        await axiosInstance.post<{ key: string }>(`/sections/${id}/generate-key`)
        revalidatePath('/dashboard')
        return { success: true }
    } catch (error: any) {
        console.error('Error creating section key:', error.response?.data)
        throw error
    }
}

export async function getAllSections(): Promise<Section[]> {
    try {
        const { data } = await axiosInstance.get('/sections?per_page=100')
        // The API returns paginated data, so we need to extract the data array
        return data.data || data
    } catch (error: any) {
        console.error('Error fetching sections:', error.response?.data)
        throw error
    }
}