'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { revalidatePath } from 'next/cache'

export interface CreateYearRequest {
    name: string;
    year: number;
    department_id: number;
}

export interface Year {
    id: number;
    name: string;
    year: number;
    department_id: number;
    created_at: string;
    updated_at: string;
}

export interface YearErrorResponse {
    message: string;
    errors?: Record<string, string[]>;
}

export async function createYear(yearData: CreateYearRequest): Promise<Year | YearErrorResponse> {
    try {
        const { data } = await axiosInstance.post<Year>(
            `/years`,
            yearData
        )
        revalidatePath('/dashboard/years')
        return data
    } catch (error: any) {
        console.error('Error creating year:', error.response?.data)
        if (error.response?.data) {
            return error.response.data as YearErrorResponse
        }
        throw error
    }
}

export async function updateYear(id: number, yearData: Partial<CreateYearRequest>): Promise<Year | YearErrorResponse> {
    try {
        const { data } = await axiosInstance.put<Year>(
            `/years/${id}`,
            yearData
        )
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error updating year:', error.response?.data)
        if (error.response?.data) {
            return error.response.data as YearErrorResponse
        }
        throw error
    }
}

export async function deleteYear(id: number): Promise<{ success: boolean }> {
    try {
        await axiosInstance.delete(`/years/${id}`)
        revalidatePath('/dashboard/years')
        return { success: true }
    } catch (error) {
        console.error('Error deleting year:', error)
        throw error
    }
}

export interface YearWithDepartment extends Year {
    department: {
        id: number;
        name: string;
    };
}

export async function getAllYears(): Promise<YearWithDepartment[]> {
    try {
        const { data } = await axiosInstance.get('/years')
        // The API returns paginated data, so we need to extract the data array
        return data.data || data
    } catch (error: any) {
        console.error('Error fetching years:', error.response?.data)
        throw error
    }
}
