import { getAllDepartmentsSimple } from "@/lib/server/actions/department/DepartmentActions";
import DepartmentsClient from "./DepartmentsClient";

export default async function DepartmentsPage() {
    let departments = [];

    try {
        departments = await getAllDepartmentsSimple();
    } catch (error) {
        console.error('Error fetching departments:', error);
        departments = [];
    }

    return <DepartmentsClient initialDepartments={departments} />;
}
