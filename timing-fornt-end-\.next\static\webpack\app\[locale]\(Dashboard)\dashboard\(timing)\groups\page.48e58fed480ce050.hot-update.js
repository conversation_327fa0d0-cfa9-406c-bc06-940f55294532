"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(timing)/groups/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/CreateGroupDialog.tsx":
/*!**************************************************************************************!*\
  !*** ./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/CreateGroupDialog.tsx ***!
  \**************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateGroupDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_ui_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ui/components/global/Buttons/Button */ \"(app-pages-browser)/./src/lib/ui/components/global/Buttons/Button.tsx\");\n/* harmony import */ var _lib_ui_components_global_Dialog_Dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ui/components/global/Dialog/Dialog */ \"(app-pages-browser)/./src/lib/ui/components/global/Dialog/Dialog.tsx\");\n/* harmony import */ var _lib_ui_forms_group_CreateGroupForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ui/forms/group/CreateGroupForm */ \"(app-pages-browser)/./src/lib/ui/forms/group/CreateGroupForm.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CreateGroupDialog(param) {\n    let { onSuccess } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCloseDialog = ()=>{\n        setOpen(false);\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_global_Buttons_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                mode: \"filled\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(timing)\\\\groups\\\\CreateGroupDialog.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 41\n                }, void 0),\n                onClick: ()=>setOpen(true),\n                children: \"Create Group\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(timing)\\\\groups\\\\CreateGroupDialog.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_global_Dialog_Dialog__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: open,\n                onClose: ()=>setOpen(false),\n                title: \"Create Group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_forms_group_CreateGroupForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onSuccess: handleCloseDialog\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(timing)\\\\groups\\\\CreateGroupDialog.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(timing)\\\\groups\\\\CreateGroupDialog.tsx\",\n                lineNumber: 25,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CreateGroupDialog, \"xG1TONbKtDWtdOTrXaTAsNhPg/Q=\");\n_c = CreateGroupDialog;\nvar _c;\n$RefreshReg$(_c, \"CreateGroupDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(timing)/groups/CreateGroupDialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8b7ea3d34a70\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWVcXEZpbmFsIHByb2plY3RcXHRpbWluZy1mb3JudC1lbmQtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4YjdlYTNkMzRhNzBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

});