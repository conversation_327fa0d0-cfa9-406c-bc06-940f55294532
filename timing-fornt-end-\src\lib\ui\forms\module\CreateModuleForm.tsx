"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { createModule } from "@/lib/server/actions/module/moduleActions";
import { CheckCircle2, AlertCircle } from "lucide-react";

const createModuleSchema = z.object({
  name: z.string().min(1, "Module name is required"),
});

type CreateModuleFormData = z.infer<typeof createModuleSchema>;

export default function CreateModuleForm({ onSuccess }: { onSuccess?: () => void }) {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CreateModuleFormData>({
    resolver: zodResolver(createModuleSchema),
  });

  const onSubmit = async (data: CreateModuleFormData) => {
    console.log('Submitting module:', data);
    setError(null);
    setSuccess(false);
    try {
      const response = await createModule({ name: data.name });
      console.log('Create module response:', response);

      // Check if response contains an error message
      if (response && 'message' in response && response.message && !response.id) {
        setError(response.message);
        return;
      }

      // Check if response contains validation errors
      if (response && 'errors' in response && response.errors) {
        const firstError = Object.values(response.errors)[0];
        setError(Array.isArray(firstError) ? firstError[0] : 'Validation failed');
        return;
      }

      setSuccess(true);
      reset();
      setTimeout(() => {
        onSuccess?.();
      }, 1000);
    } catch (e: any) {
      console.error('Create module error:', e);
      setError(e.message || "Failed to create module");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
      {error && (
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in">
          <CheckCircle2 size={20} />
          <span>Module created successfully!</span>
        </div>
      )}
      <div className="flex flex-col gap-2">
        <label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Module Name
        </label>
        <input
          type="text"
          id="name"
          {...register("name")}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
          placeholder="e.g., Algorithms, Linear Algebra"
        />
        {errors.name && <span className="text-red-500 text-sm">{errors.name.message}</span>}
      </div>
      <Button type="submit" mode="filled" disabled={isSubmitting}>
        {isSubmitting ? "Creating..." : "Create Module"}
      </Button>
    </form>
  );
}
