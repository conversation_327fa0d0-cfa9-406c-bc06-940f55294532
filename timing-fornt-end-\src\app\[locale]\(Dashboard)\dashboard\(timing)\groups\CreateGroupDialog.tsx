"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateGroupForm from "@/lib/ui/forms/group/CreateGroupForm";
import { Plus } from "lucide-react";

interface CreateGroupDialogProps {
    onSuccess?: () => void;
}

export default function CreateGroupDialog({ onSuccess }: CreateGroupDialogProps) {
    const [open, setOpen] = useState(false);

    const handleCloseDialog = () => {
        setOpen(false);
        onSuccess?.();
    };

    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Group
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Group">
                <CreateGroupForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
}