"use client";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CheckCircle2, AlertCircle } from "lucide-react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import { SimpleSelect } from "@/lib/ui/components/global/Inputs/SimpleSelect";
import { createSection } from "@/lib/server/actions/section/sectionActions";
import { getAllDepartments } from "@/lib/server/actions/department/DepartmentActions";
import { Department, Year } from "@/lib/server/types/departments/allDepartments";

const createSectionSchema = z.object({
    number: z.coerce.number().min(1, "Section number is required"),
    year_id: z.string().min(1, "Year is required"),
    department_id: z.string().min(1, "Department is required"),
});

type CreateSectionFormData = z.infer<typeof createSectionSchema>;

interface CreateSectionFormProps {
    onSuccess?: () => void;
}

export default function CreateSectionForm({ onSuccess }: CreateSectionFormProps) {
    const [departments, setDepartments] = useState<Department[]>([]);
    const [years, setYears] = useState<Year[]>([]);
    const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

    const {
        register,
        handleSubmit,
        watch,
        setValue,
        formState: { errors, isSubmitting },
    } = useForm<CreateSectionFormData>({
        resolver: zodResolver(createSectionSchema),
    });

    useEffect(() => {
        const fetchDepartments = async () => {
            try {
                const data = await getAllDepartments();
                setDepartments(data.departments);
            } catch (error) {
                console.error('Error fetching departments:', error);
            }
        };
        fetchDepartments();
    }, []);

    const onSubmit = async (data: CreateSectionFormData) => {
        try {
            setSubmitStatus('idle');
            await createSection({
                number: data.number,
                year_id: Number(data.year_id),
            });
            setSubmitStatus('success');
            setTimeout(() => {
                onSuccess?.();
            }, 1000);
        } catch (error) {
            console.error('Error creating section:', error);
            setSubmitStatus('error');
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
            {submitStatus === 'success' && (
                <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                    <CheckCircle2 size={20} />
                    <span>Section created successfully!</span>
                </div>
            )}
            {submitStatus === 'error' && (
                <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                    <AlertCircle size={20} />
                    <span>Error creating section. Please try again.</span>
                </div>
            )}

            <Input
                label="number"
                title="Section Number"
                type="number"
                placeholder="Enter section number"
                error={errors.number?.message}
                register={register}
            />

            <SimpleSelect
                title="Department"
                label="department_id"
                register={register("department_id")}
                error={errors.department_id?.message}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                    const departmentId = e.target.value;
                    setValue("department_id", departmentId);
                    if (departmentId) {
                        const selectedDepartment = departments.find(dept => dept.id === +departmentId);
                        setYears(selectedDepartment ? selectedDepartment.years : []);
                    } else {
                        setYears([]);
                    }
                }}
            >
                <option value="">Select Department</option>
                {departments.map((department) => (
                    <option key={department.id} value={department.id}>
                        {department.name}
                    </option>
                ))}
            </SimpleSelect>

            {watch('department_id') && (
                <SimpleSelect
                    title="Year"
                    label="year_id"
                    register={register("year_id")}
                    error={errors.year_id?.message}
                >
                    <option value="">Select Year</option>
                    {years.map((year) => (
                        <option key={year.id} value={year.id}>
                            {year.name}
                        </option>
                    ))}
                </SimpleSelect>
            )}

            <Button
                type="submit"
                mode="filled"
                disabled={isSubmitting}
            >
                {isSubmitting ? "Creating..." : "Create Section"}
            </Button>
        </form>
    );
}
