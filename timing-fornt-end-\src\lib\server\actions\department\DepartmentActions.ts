'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { DepartmentResponse } from '../../types/departments/allDepartments'
import { revalidatePath } from 'next/cache'

export async function getAllDepartments(): Promise<DepartmentResponse> {
    try {
        const { data } = await axiosInstance.get<DepartmentResponse>(
            `/allDepartments`,
        )
        return data
    } catch (error: any) {
        console.error('Error fetching departments:', error.response?.data)
        throw error
    }
}

export interface Department {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
}

export async function createDepartment(departmentData: { name: string }): Promise<any> {
    try {
        const { data } = await axiosInstance.post(
            `/departments`,
            departmentData
        )
        revalidatePath('/dashboard/departements')
        return data
    } catch (error: any) {
        console.error('Error creating department:', error.response?.data)
        if (error.response?.data) {
            return error.response.data
        }
        throw error
    }
}

export async function getAllDepartmentsSimple(): Promise<Department[]> {
    try {
        const { data } = await axiosInstance.get('/departments')
        // The API returns paginated data, so we need to extract the data array
        return data.data || data
    } catch (error: any) {
        console.error('Error fetching departments:', error.response?.data)
        throw error
    }
}

export async function deleteDepartment(id: number): Promise<{ success: boolean }> {
    try {
        await axiosInstance.delete(`/departments/${id}`)
        revalidatePath('/dashboard/departements')
        return { success: true }
    } catch (error) {
        console.error('Error deleting department:', error)
        throw error
    }
}
