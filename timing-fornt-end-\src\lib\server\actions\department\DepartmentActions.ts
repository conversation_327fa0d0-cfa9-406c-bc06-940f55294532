'use server'

import axiosInstance from '@/lib/server/tools/axios'
import { DepartmentResponse } from '../../types/departments/allDepartments'
import { revalidatePath } from 'next/cache'

export async function getAllDepartments(): Promise<DepartmentResponse> {
    try {
        const { data } = await axiosInstance.get<DepartmentResponse>(
            `/allDepartments`,
        )
        return data
    } catch (error: any) {
        console.error('Error fetching departments:', error.response?.data)
        throw error
    }
}

export async function createDepartment(departmentData: { name: string }): Promise<any> {
    try {
        const { data } = await axiosInstance.post(
            `/departments`,
            departmentData
        )
        revalidatePath('/dashboard')
        return data
    } catch (error: any) {
        console.error('Error creating department:', error.response?.data)
        if (error.response?.data) {
            return error.response.data
        }
        throw error
    }
}
