"use client";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CheckCircle2, AlertCircle } from "lucide-react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { Input } from "@/lib/ui/components/global/Inputs/inputs";
import { SimpleSelect } from "@/lib/ui/components/global/Inputs/SimpleSelect";
import { createGroup } from "@/lib/server/actions/group/groupActions";
import { getAllDepartments } from "@/lib/server/actions/department/DepartmentActions";
import { Department, Section, Year } from "@/lib/server/types/departments/allDepartments";

const createGroupSchema = z.object({
    number: z.coerce.number().min(1, "Group number is required"),
    section_id: z.string().min(1, "Section is required"),
    department_id: z.string().min(1, "Department is required"),
    year_id: z.string().min(1, "Year is required"),
});

type CreateGroupFormData = z.infer<typeof createGroupSchema>;

interface CreateGroupFormProps {
    onSuccess?: () => void;
}

export default function CreateGroupForm({ onSuccess }: CreateGroupFormProps) {
    const [departments, setDepartments] = useState<Department[]>([]);
    const [years, setYears] = useState<Year[]>([]);
    const [sections, setSections] = useState<Section[]>([]);
    const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

    const {
        register,
        handleSubmit,
        watch,
        setValue,
        formState: { errors, isSubmitting },
    } = useForm<CreateGroupFormData>({
        resolver: zodResolver(createGroupSchema),
    });

    useEffect(() => {
        const fetchDepartments = async () => {
            try {
                const data = await getAllDepartments();
                setDepartments(data.departments);
            } catch (error) {
                console.error('Error fetching departments:', error);
            }
        };
        fetchDepartments();
    }, []);

    const onSubmit = async (data: CreateGroupFormData) => {
        try {
            setSubmitStatus('idle');
            await createGroup({
                number: data.number,
                section_id: Number(data.section_id),
            });
            setSubmitStatus('success');
            setTimeout(() => {
                onSuccess?.();
            }, 1000);
        } catch (error) {
            console.error('Error creating group:', error);
            setSubmitStatus('error');
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
            {submitStatus === 'success' && (
                <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                    <CheckCircle2 size={20} />
                    <span>Group created successfully!</span>
                </div>
            )}
            {submitStatus === 'error' && (
                <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                    <AlertCircle size={20} />
                    <span>Error creating group. Please try again.</span>
                </div>
            )}

            <Input
                label="number"
                title="Group Number"
                type="number"
                placeholder="Enter group number"
                error={errors.number?.message}
                register={register}
            />

            <SimpleSelect
                title="Department"
                label="department_id"
                register={register("department_id")}
                error={errors.department_id?.message}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                    const departmentId = e.target.value;
                    setValue("department_id", departmentId);
                    if (departmentId) {
                        const selectedDepartment = departments.find(dept => dept.id === +departmentId);
                        setYears(selectedDepartment ? selectedDepartment.years : []);
                    } else {
                        setYears([]);
                    }
                    setSections([]);
                }}
            >
                <option value="">Select Department</option>
                {departments.map((department) => (
                    <option key={department.id} value={department.id}>
                        {department.name}
                    </option>
                ))}
            </SimpleSelect>

            {watch('department_id') && (
                <SimpleSelect
                    title="Year"
                    label="year_id"
                    register={register("year_id")}
                    error={errors.year_id?.message}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                        const yearId = e.target.value;
                        setValue("year_id", yearId);
                        if (yearId) {
                            const selectedYear = years.find(year => year.id === +yearId);
                            setSections(selectedYear ? selectedYear.sections : []);
                        } else {
                            setSections([]);
                        }
                    }}
                >
                    <option value="">Select Year</option>
                    {years.map((year) => (
                        <option key={year.id} value={year.id}>
                            {year.name}
                        </option>
                    ))}
                </SimpleSelect>
            )}

            {watch('year_id') && (
                <SimpleSelect
                    title="Section"
                    label="section_id"
                    register={register("section_id")}
                    error={errors.section_id?.message}
                >
                    <option value="">Select Section</option>
                    {sections.map((section) => (
                        <option key={section.id} value={section.id}>
                            Section {section.number}
                        </option>
                    ))}
                </SimpleSelect>
            )}

            <Button
                type="submit"
                mode="filled"
                disabled={isSubmitting}
            >
                {isSubmitting ? "Creating..." : "Create Group"}
            </Button>
        </form>
    );
}
