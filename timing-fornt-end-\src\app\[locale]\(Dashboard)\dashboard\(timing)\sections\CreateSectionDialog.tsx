"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateSectionForm from "@/lib/ui/forms/section/CreateSectionForm";
import { Plus } from "lucide-react";

interface CreateSectionDialogProps {
    onSuccess?: () => void;
}

export default function CreateSectionDialog({ onSuccess }: CreateSectionDialogProps) {
    const [open, setOpen] = useState(false);

    const handleCloseDialog = () => {
        setOpen(false);
        onSuccess?.();
    };

    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Section
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Section">
                <CreateSectionForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
}