"use client";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Button from "@/lib/ui/components/global/Buttons/Button";
import { getAllDepartments } from "@/lib/server/actions/department/DepartmentActions";
import { createGroup } from "@/lib/server/actions/group/groupActions";
import { CheckCircle2, AlertCircle } from "lucide-react";
import Dialog from "@/lib/ui/components/global/Dialogs/Dialog";
import CreateGroupForm from "./CreateGroupForm";

const createGroupSchema = z.object({
  number: z.coerce.number().min(1, "Group number is required"),
  department_id: z.string().min(1, "Department is required"),
  year_id: z.string().min(1, "Year is required"),
  section_id: z.string().min(1, "Section is required"),
  students_count: z.coerce.number().min(1, "Number of students is required"),
});

type CreateGroupFormData = z.infer<typeof createGroupSchema>;

export default function CreateGroupForm({ onSuccess }: { onSuccess?: () => void }) {
  const [departments, setDepartments] = useState<any[]>([]);
  const [years, setYears] = useState<any[]>([]);
  const [sections, setSections] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CreateGroupFormData>({
    resolver: zodResolver(createGroupSchema),
  });

  useEffect(() => {
    getAllDepartments().then((data) => setDepartments(data.departments));
  }, []);

  useEffect(() => {
    const depId = watch("department_id");
    if (depId) {
      const dep = departments.find((d) => d.id === +depId);
      setYears(dep ? dep.years : []);
      setValue("year_id", "");
      setSections([]);
      setValue("section_id", "");
    }
  }, [watch("department_id"), departments, setValue]);

  useEffect(() => {
    const yearId = watch("year_id");
    if (yearId) {
      const year = years.find((y) => y.id === +yearId);
      setSections(year ? year.sections : []);
      setValue("section_id", "");
    }
  }, [watch("year_id"), years, setValue]);

  const onSubmit = async (data: CreateGroupFormData) => {
    console.log('Submitting group:', data);
    setError(null);
    setSuccess(false);
    try {
      const response = await createGroup({
        number: data.number,
        section_id: +data.section_id,
        students_count: data.students_count,
      });
      console.log('Create group response:', response);
      if (response && (response as any).message) {
        setError((response as any).message);
        return;
      }
      setSuccess(true);
      reset();
      onSuccess?.();
    } catch (e: any) {
      console.error('Create group error:', e);
      setError(e.message || "Failed to create group");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full max-w-md">
      {error && (
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400 animate-fade-in">
          <AlertCircle size={20} />
          <span>{error}</span>
        </div>
      )}
      {success && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 animate-fade-in">
          <CheckCircle2 size={20} />
          <span>Group created successfully!</span>
        </div>
      )}
      <label>
        Group Number
        <input type="number" {...register("number")} className="input" />
        {errors.number && <span className="text-red-500">{errors.number.message}</span>}
      </label>
      <label>
        Department
        <select {...register("department_id")} className="input">
          <option value="">Select Department</option>
          {departments.map((dep) => (
            <option key={dep.id} value={dep.id}>{dep.name}</option>
          ))}
        </select>
        {errors.department_id && <span className="text-red-500">{errors.department_id.message}</span>}
      </label>
      <label>
        Year
        <select {...register("year_id")} className="input">
          <option value="">Select Year</option>
          {years.map((year) => (
            <option key={year.id} value={year.id}>{year.name}</option>
          ))}
        </select>
        {errors.year_id && <span className="text-red-500">{errors.year_id.message}</span>}
      </label>
      <label>
        Section
        <select {...register("section_id")} className="input">
          <option value="">Select Section</option>
          {sections.map((section) => (
            <option key={section.id} value={section.id}>{section.number}</option>
          ))}
        </select>
        {errors.section_id && <span className="text-red-500">{errors.section_id.message}</span>}
      </label>
      <label>
        Number of Students
        <input type="number" {...register("students_count")} className="input" />
        {errors.students_count && <span className="text-red-500">{errors.students_count.message}</span>}
      </label>
      <Button type="submit" mode="filled" disabled={isSubmitting}>
        {isSubmitting ? "Creating..." : "Create Group"}
      </Button>
    </form>
  );
} 