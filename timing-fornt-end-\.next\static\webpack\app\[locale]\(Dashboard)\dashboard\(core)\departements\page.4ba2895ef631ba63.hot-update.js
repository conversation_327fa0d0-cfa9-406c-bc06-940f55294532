"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(core)/departements/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/departements/DepartmentsClient.tsx":
/*!******************************************************************************************!*\
  !*** ./src/app/[locale]/(Dashboard)/dashboard/(core)/departements/DepartmentsClient.tsx ***!
  \******************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DepartmentsClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ui/components/local/Dashboard/DashCrudContent */ \"(app-pages-browser)/./src/lib/ui/components/local/Dashboard/DashCrudContent.tsx\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Pencil,Trash,UserPen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _CreateDepartmentDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateDepartmentDialog */ \"(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/departements/CreateDepartmentDialog.tsx\");\n/* harmony import */ var _lib_server_actions_department_DepartmentActions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/server/actions/department/DepartmentActions */ \"(app-pages-browser)/./src/lib/server/actions/department/DepartmentActions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DepartmentsClient(param) {\n    let { initialDepartments } = param;\n    _s();\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDepartments);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deleteDialog, setDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        department: null,\n        isDeleting: false\n    });\n    const refreshDepartments = async ()=>{\n        setIsRefreshing(true);\n        try {\n            const updatedDepartments = await (0,_lib_server_actions_department_DepartmentActions__WEBPACK_IMPORTED_MODULE_5__.getAllDepartmentsSimple)();\n            setDepartments(updatedDepartments);\n        } catch (error) {\n            console.error('Error refreshing departments:', error);\n        } finally{\n            setIsRefreshing(false);\n        }\n    };\n    const handleDeleteClick = (department)=>{\n        setDeleteDialog({\n            isOpen: true,\n            department,\n            isDeleting: false\n        });\n    };\n    const handleDeleteConfirm = async ()=>{\n        if (!deleteDialog.department) return;\n        setDeleteDialog((prev)=>({\n                ...prev,\n                isDeleting: true\n            }));\n        try {\n            await (0,_lib_server_actions_department_DepartmentActions__WEBPACK_IMPORTED_MODULE_5__.deleteDepartment)(deleteDialog.department.id);\n            // Remove the department from the local state immediately\n            setDepartments((prev)=>prev.filter((d)=>d.id !== deleteDialog.department.id));\n            setDeleteDialog({\n                isOpen: false,\n                department: null,\n                isDeleting: false\n            });\n        } catch (error) {\n            console.error('Error deleting department:', error);\n            setDeleteDialog((prev)=>({\n                    ...prev,\n                    isDeleting: false\n                }));\n        }\n    };\n    const handleDeleteCancel = ()=>{\n        if (!deleteDialog.isDeleting) {\n            setDeleteDialog({\n                isOpen: false,\n                department: null,\n                isDeleting: false\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContent, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContenTitle, {\n                children: \"Departments\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                lineNumber: 82,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentStat, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentStatItem, {\n                    title: \"Total Departments\",\n                    value: departments.length.toString(),\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 80\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 27\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                lineNumber: 83,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentAction, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateDepartmentDialog__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onSuccess: refreshDepartments\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                lineNumber: 90,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.DashContentTable, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableThead, {\n                        list: [\n                            'Department Name',\n                            'Settings'\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: departments.map((department)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTr, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTdMain, {\n                                        value: department.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ui_components_local_Dashboard_DashCrudContent__WEBPACK_IMPORTED_MODULE_2__.TableTd, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/departments/\".concat(department.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"text-green-700 dark:text-green-400\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/dashboard/departments/\".concat(department.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Pencil_Trash_UserPen_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"text-error dark:text-dark-error\",\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, department.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 25\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                lineNumber: 93,\n                columnNumber: 13\n            }, this),\n            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4 text-gray-500\",\n                children: \"Refreshing departments...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n                lineNumber: 114,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fin proj copie\\\\Final project\\\\timing-fornt-end-\\\\src\\\\app\\\\[locale]\\\\(Dashboard)\\\\dashboard\\\\(core)\\\\departements\\\\DepartmentsClient.tsx\",\n        lineNumber: 81,\n        columnNumber: 9\n    }, this);\n}\n_s(DepartmentsClient, \"MgTEaC0hz6x2WT0tCQP82v+mm9E=\");\n_c = DepartmentsClient;\nvar _c;\n$RefreshReg$(_c, \"DepartmentsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(Dashboard)/dashboard/(core)/departements/DepartmentsClient.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0c2cbec5d08a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWVcXEZpbmFsIHByb2plY3RcXHRpbWluZy1mb3JudC1lbmQtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwYzJjYmVjNWQwOGFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

});