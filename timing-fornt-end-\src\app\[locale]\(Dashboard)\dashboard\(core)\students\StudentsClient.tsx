"use client";
import { useState } from "react";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    Dash<PERSON>ontentA<PERSON>,
    DashContenTitle,
    DashContentStat,
    DashContentStatItem,
    DashContentTable,
    TableTd,
    TableTdMain,
    TableThead,
    TableTr
} from "@/lib/ui/components/local/Dashboard/DashCrudContent";
import { Pencil, Trash, UserPen } from "lucide-react";
import Link from "next/link";
import CreateStudentDialog from "./CreateStudentDialog";
import { getAllStudents, Student, deleteStudent } from "@/lib/server/actions/student/studentActions";
import ConfirmDialog from "@/lib/ui/components/global/Dialog/ConfirmDialog";

interface StudentsClientProps {
    initialStudents: Student[];
}

export default function StudentsClient({ initialStudents }: StudentsClientProps) {
    const [students, setStudents] = useState<Student[]>(initialStudents);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [deleteDialog, setDeleteDialog] = useState<{
        isOpen: boolean;
        student: Student | null;
        isDeleting: boolean;
    }>({
        isOpen: false,
        student: null,
        isDeleting: false
    });

    const refreshStudents = async () => {
        setIsRefreshing(true);
        try {
            const updatedStudents = await getAllStudents();
            setStudents(updatedStudents);
        } catch (error) {
            console.error('Error refreshing students:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleDeleteClick = (student: Student) => {
        setDeleteDialog({
            isOpen: true,
            student,
            isDeleting: false
        });
    };

    const handleDeleteConfirm = async () => {
        if (!deleteDialog.student) return;

        setDeleteDialog(prev => ({ ...prev, isDeleting: true }));
        
        try {
            await deleteStudent(deleteDialog.student.id);
            // Remove the student from the local state immediately
            setStudents(prev => prev.filter(s => s.id !== deleteDialog.student!.id));
            setDeleteDialog({ isOpen: false, student: null, isDeleting: false });
        } catch (error) {
            console.error('Error deleting student:', error);
            setDeleteDialog(prev => ({ ...prev, isDeleting: false }));
        }
    };

    const handleDeleteCancel = () => {
        if (!deleteDialog.isDeleting) {
            setDeleteDialog({ isOpen: false, student: null, isDeleting: false });
        }
    };

    return (
        <DashContent>
            <DashContenTitle>Students</DashContenTitle>
            <DashContentStat>
                <DashContentStatItem 
                    title="Total Students" 
                    value={students.length.toString()} 
                    icon={<UserPen size={80} />} 
                />
            </DashContentStat>
            <DashContentAction>
                <CreateStudentDialog onSuccess={refreshStudents} />
            </DashContentAction>
            <DashContentTable>
                <TableThead list={['Name', 'Username', 'Group', 'Section', 'Year - Department', 'Settings']} />
                <tbody>
                    {students.map((student) => (
                        <TableTr key={student.id}>
                            <TableTdMain value={`${student.name} ${student.last}`} />
                            <TableTd>{student.username}</TableTd>
                            <TableTd>{student.group?.number || 'No Group'}</TableTd>
                            <TableTd>{student.group?.section?.number || 'No Section'}</TableTd>
                            <TableTd>
                                {student.group?.section?.year?.name || 'No Year'} - {student.group?.section?.year?.department?.name || 'No Department'}
                            </TableTd>
                            <TableTd>
                                <div className="flex items-center gap-1">
                                    <Link href={`/dashboard/students/${student.id}`}>
                                        <Pencil className="text-green-700 dark:text-green-400" size={16} />
                                    </Link>
                                    <button
                                        onClick={() => handleDeleteClick(student)}
                                        className="p-1 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                                        title="Delete student"
                                    >
                                        <Trash className="text-red-600 dark:text-red-400" size={16} />
                                    </button>
                                </div>
                            </TableTd>
                        </TableTr>
                    ))}
                </tbody>
            </DashContentTable>
            {isRefreshing && (
                <div className="text-center py-4 text-gray-500">
                    Refreshing students...
                </div>
            )}
            
            <ConfirmDialog
                isOpen={deleteDialog.isOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                title="Delete Student"
                message={`Are you sure you want to delete "${deleteDialog.student?.name} ${deleteDialog.student?.last}"? This action cannot be undone.`}
                confirmText="Delete"
                cancelText="Cancel"
                isLoading={deleteDialog.isDeleting}
                variant="danger"
            />
        </DashContent>
    );
}
