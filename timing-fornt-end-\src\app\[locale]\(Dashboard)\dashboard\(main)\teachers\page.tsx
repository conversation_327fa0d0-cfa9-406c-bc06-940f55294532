import { getAllTeachers } from "@/lib/server/actions/teacher/teacherActions";
import TeachersClient from "./TeachersClient";

export default async function TeachersPage() {
    let teachers = [];

    try {
        teachers = await getAllTeachers();
    } catch (error) {
        console.error('Error fetching teachers:', error);
        teachers = [];
    }

    return <TeachersClient initialTeachers={teachers} />;
}