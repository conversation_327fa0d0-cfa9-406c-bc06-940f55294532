"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(Dashboard)/dashboard/(main)/students/page",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f835499bc1d6\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByb1xcRGVza3RvcFxcZmluIHByb2ogY29waWVcXEZpbmFsIHByb2plY3RcXHRpbWluZy1mb3JudC1lbmQtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmODM1NDk5YmMxZDZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/server/actions/department/DepartmentActions.ts":
/*!****************************************************************!*\
  !*** ./src/lib/server/actions/department/DepartmentActions.ts ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDepartment: () => (/* binding */ createDepartment),\n/* harmony export */   deleteDepartment: () => (/* binding */ deleteDepartment),\n/* harmony export */   getAllDepartments: () => (/* binding */ getAllDepartments),\n/* harmony export */   getAllDepartmentsSimple: () => (/* binding */ getAllDepartmentsSimple)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"0035c0f11b216454fd195a42d51a7e6a93b0d5ab73\":\"getAllDepartmentsSimple\",\"00ebbc7d72703fbe432736f510141cb1b1a25567bc\":\"getAllDepartments\",\"402707a3846d58fabe7568c444e7c0961fa37be9ce\":\"createDepartment\",\"4034e31912129edbb5efd3a26d125eaa1123936115\":\"deleteDepartment\"} */ \nvar getAllDepartments = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00ebbc7d72703fbe432736f510141cb1b1a25567bc\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAllDepartments\");\nvar createDepartment = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"402707a3846d58fabe7568c444e7c0961fa37be9ce\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createDepartment\");\nvar getAllDepartmentsSimple = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"0035c0f11b216454fd195a42d51a7e6a93b0d5ab73\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAllDepartmentsSimple\");\nvar deleteDepartment = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4034e31912129edbb5efd3a26d125eaa1123936115\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteDepartment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/server/actions/department/DepartmentActions.ts\n"));

/***/ })

});