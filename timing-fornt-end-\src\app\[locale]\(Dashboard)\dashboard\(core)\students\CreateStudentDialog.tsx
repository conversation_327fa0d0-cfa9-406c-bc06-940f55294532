"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateStudentForm from "./CreateStudentForm";
import { Plus } from "lucide-react";

interface CreateStudentDialogProps {
    onSuccess?: () => void;
}

export default function CreateStudentDialog({ onSuccess }: CreateStudentDialogProps) {
    const [open, setOpen] = useState(false);
    
    const handleCloseDialog = () => {
        setOpen(false);
        onSuccess?.();
    };
    
    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Student
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Student">
                <CreateStudentForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
}
