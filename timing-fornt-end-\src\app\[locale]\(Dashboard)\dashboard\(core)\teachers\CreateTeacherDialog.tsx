"use client";
import { useState } from "react";
import Button from "@/lib/ui/components/global/Buttons/Button";
import Dialog from "@/lib/ui/components/global/Dialog/Dialog";
import CreateTeacherForm from "./CreateTeacherForm";
import { Plus } from "lucide-react";

interface CreateTeacherDialogProps {
    onSuccess?: () => void;
}

export default function CreateTeacherDialog({ onSuccess }: CreateTeacherDialogProps) {
    const [open, setOpen] = useState(false);
    
    const handleCloseDialog = () => {
        setOpen(false);
        onSuccess?.();
    };
    
    return (
        <>
            <Button mode="filled" icon={<Plus />} onClick={() => setOpen(true)}>
                Create Teacher
            </Button>
            <Dialog isOpen={open} onClose={() => setOpen(false)} title="Create Teacher">
                <CreateTeacherForm onSuccess={handleCloseDialog} />
            </Dialog>
        </>
    );
}
